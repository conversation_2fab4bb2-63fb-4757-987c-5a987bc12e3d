"use client"

import {
  Background,
  Controls,
  MarkerType,
  MiniMap,
  ReactFlow,
  ReactFlowProvider,
  applyEdgeChanges,
  applyNodeChanges,
  useReactFlow,
  type Node,
  type OnConnect,
  type OnSelectionChangeParams,
} from "@xyflow/react"
import "@xyflow/react/dist/style.css"
import { useCallback, useEffect, useRef, useState } from "react"
import { useKeyboardShortcuts } from "../../hooks/useKeyboardShortcuts"
import { useToast } from "../../hooks/useToast"
import { applyAutoLayoutSmooth, createDebouncedAutoLayout } from "../../lib/autoLayout"
import { useEditorStore } from "../../stores/editorStore"
import { ToastContainer } from "../ui/Toast"
import { CustomNode } from "./CustomNode"
import { ShoshinEdge } from "./ShoshinEdge"

const nodeTypes = {
  custom: CustomNode,
}

const edgeTypes = {
  shoshin: ShoshinEdge,
}



function MainCanvasInner() {
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const [reactFlowInstance, setReactFlowInstance] = useState<unknown>(null)
  const { fitView } = useReactFlow()

  // Use Zustand store for state management
  const {
    nodes,
    edges,
    setNodes,
    setEdges,
    updateSelection,
    addNode,
    connectNodes
  } = useEditorStore()

  // Initialize hooks
  const { toasts, removeToast } = useToast()

  // Enable keyboard shortcuts
  useKeyboardShortcuts()

  // ReactFlow change handlers
  const onNodesChange = useCallback((changes: Parameters<typeof applyNodeChanges>[0]) => {
    setNodes(applyNodeChanges(changes, nodes))
  }, [nodes, setNodes])

  const onEdgesChange = useCallback((changes: Parameters<typeof applyEdgeChanges>[0]) => {
    setEdges(applyEdgeChanges(changes, edges))
  }, [edges, setEdges])

  // Selection change handler
  const onSelectionChange = useCallback((params: OnSelectionChangeParams) => {
    updateSelection(params.nodes, params.edges)
  }, [updateSelection])





  // Enhanced auto layout function with smooth animations
  const applyAutoLayout = useCallback(() => {
    if (nodes.length === 0) return

    applyAutoLayoutSmooth(
      nodes,
      edges,
      setNodes,
      fitView,
      {
        horizontalSpacing: 360,
        verticalSpacing: 240,
        startX: 150,
        startY: 150,
        animationDuration: 500,
        handleOrientation: 'horizontal',
        minimizeEdgeCrossings: true,
        edgeCrossingIterations: 3,
        dynamicSpacing: true,
        edgeNodeSpacing: 20, // Increased to 20px for better visual separation
        edgeNodeCollisionIterations: 8, // More iterations for thorough collision resolution
        edgeEdgeSpacing: 10, // Minimum 10px distance between edges with different source/target nodes
        edgeEdgeCollisionIterations: 5, // Iterations for edge-edge collision resolution
        onComplete: (finalPositions) => {
          console.log('Auto layout completed', {
            nodesPositioned: finalPositions.size,
            orientation: 'horizontal',
            edgeCrossingMinimizationEnabled: true,
            edgeNodeSpacingEnabled: true,
            edgeEdgeSpacingEnabled: true
          })
        }
      }
    )
  }, [nodes, edges, setNodes, fitView])

  // Create debounced auto layout to prevent rapid triggering
  const debouncedAutoLayout = useCallback(
    createDebouncedAutoLayout(applyAutoLayout),
    [applyAutoLayout]
  )

  // Listen for auto layout events with debouncing
  useEffect(() => {
    let cleanup: (() => void) | null = null

    const handleAutoLayout = () => {
      if (cleanup) cleanup()
      cleanup = debouncedAutoLayout()
    }

    window.addEventListener('trigger-auto-layout', handleAutoLayout)
    return () => {
      window.removeEventListener('trigger-auto-layout', handleAutoLayout)
      if (cleanup) cleanup()
    }
  }, [debouncedAutoLayout])

  // Auto-apply layout when editor is loaded/refreshed if nodes exist
  const [hasAutoLayoutApplied, setHasAutoLayoutApplied] = useState(false)

  useEffect(() => {
    // Only run this effect once when the component mounts and ReactFlow is ready
    // Check if we have nodes, ReactFlow instance is ready, and we haven't applied auto-layout yet
    if (nodes.length > 0 && reactFlowInstance && !hasAutoLayoutApplied) {
      // Add a small delay to ensure ReactFlow is fully initialized
      const timeoutId = setTimeout(() => {
        console.log('Auto-applying layout on editor load/refresh with', nodes.length, 'nodes')
        applyAutoLayout()
        setHasAutoLayoutApplied(true)
      }, 100)

      return () => clearTimeout(timeoutId)
    }
  }, [nodes.length, reactFlowInstance, hasAutoLayoutApplied, applyAutoLayout])

  const onConnect: OnConnect = useCallback(
    (params) => {
      connectNodes(params)
    },
    [connectNodes]
  )

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = "move"
  }, [])

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault()

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect()
      const data = event.dataTransfer.getData("application/reactflow")

      if (typeof data === "undefined" || !data || !reactFlowBounds) {
        return
      }

      const blockData = JSON.parse(data)
      const position = (reactFlowInstance as any)?.screenToFlowPosition({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      })

      const newNode: Node = {
        id: `${blockData.type}-${Date.now()}`,
        type: "custom",
        position,
        data: {
          label: blockData.name,
          type: blockData.type,
          description: blockData.description,
        },
      }

      addNode(newNode)
    },
    [reactFlowInstance, addNode]
  )

  return (
    <div className="w-full h-full" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={setReactFlowInstance}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onSelectionChange={onSelectionChange}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        className="bg-background"
        defaultEdgeOptions={{
          type: 'shoshin',
          animated: true,
          markerEnd: {
            type: MarkerType.ArrowClosed,
            color: '#9ca3af',
          },
        }}
        connectionLineStyle={{
          stroke: '#9ca3af',
          strokeWidth: 2,
          strokeDasharray: '5,5'
        }}
        snapToGrid={true}
        snapGrid={[15, 15]}
      >
        <Background
          color="currentColor"
          gap={20}
          size={1}
        />
        <Controls
          position="top-left"
          className="bg-background border-border [&>button]:bg-neutral-100 dark:[&>button]:bg-neutral-800 [&>button]:border-border [&>button]:text-foreground [&>button:hover]:bg-neutral-200 dark:[&>button:hover]:bg-neutral-700"
          style={{
            left: '96px', // Offset by outermost sidebar width (64px) + additional padding (32px)
            top: '16px'   // Add some top margin
          }}
        />
        <MiniMap
          className="bg-background border-border"
          nodeColor="rgb(168 85 247)"
          maskColor="rgba(0, 0, 0, 0.3)"
          pannable
          zoomable
        />
      </ReactFlow>
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  )
}

export function MainCanvas() {
  return (
    <ReactFlowProvider>
      <MainCanvasInner />
    </ReactFlowProvider>
  )
}
